"use client"

import { useState } from 'react'
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import { SortableContext, arrayMove } from '@dnd-kit/sortable'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Plus, MoreHorizontal, Clock, User } from 'lucide-react'
import { TaskCard } from './task-card'

interface Task {
  id: string
  title: string
  description?: string
  status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  assignee?: {
    id: string
    name: string
    avatar?: string
  }
  creator: {
    id: string
    name: string
    avatar?: string
  }
  dueDate?: string
  project: {
    id: string
    name: string
  }
  comments: number
  attachments: number
}

interface Column {
  id: string
  title: string
  status: Task['status']
  taskIds: string[]
}

interface KanbanBoardProps {
  tasks: Task[]
  onTaskUpdate?: (taskId: string, newStatus: Task['status']) => void
  onTaskCreate?: (status: Task['status']) => void
}

const getStatusColor = (status: Task['status']) => {
  switch (status) {
    case 'TODO': return 'bg-gray-500'
    case 'IN_PROGRESS': return 'bg-blue-500'
    case 'REVIEW': return 'bg-yellow-500'
    case 'DONE': return 'bg-green-500'
    case 'CANCELLED': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

const getPriorityColor = (priority: Task['priority']) => {
  switch (priority) {
    case 'LOW': return 'bg-green-100 text-green-800'
    case 'MEDIUM': return 'bg-yellow-100 text-yellow-800'
    case 'HIGH': return 'bg-orange-100 text-orange-800'
    case 'CRITICAL': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

export function KanbanBoard({ tasks, onTaskUpdate, onTaskCreate }: KanbanBoardProps) {
  const [columns] = useState<Column[]>([
    { id: 'todo', title: 'To Do', status: 'TODO', taskIds: [] },
    { id: 'in-progress', title: 'In Progress', status: 'IN_PROGRESS', taskIds: [] },
    { id: 'review', title: 'Review', status: 'REVIEW', taskIds: [] },
    { id: 'done', title: 'Done', status: 'DONE', taskIds: [] },
  ])

  const [activeTask, setActiveTask] = useState<Task | null>(null)

  // Group tasks by status
  const tasksByStatus = tasks.reduce((acc, task) => {
    if (!acc[task.status]) {
      acc[task.status] = []
    }
    acc[task.status].push(task)
    return acc
  }, {} as Record<Task['status'], Task[]>)

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  )

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    const task = tasks.find(t => t.id === active.id)
    if (task) {
      setActiveTask(task)
    }
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (over && active.id !== over.id) {
      const task = tasks.find(t => t.id === active.id)
      const column = columns.find(c => c.id === over.id)

      if (task && column && task.status !== column.status) {
        onTaskUpdate?.(task.id, column.status)
      }
    }

    setActiveTask(null)
  }

  return (
    <div className="w-full">
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex gap-4 overflow-x-auto pb-4">
          {columns.map((column) => (
            <div
              key={column.id}
              className="flex-shrink-0 w-80 bg-gray-50 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${getStatusColor(column.status)}`} />
                  <h3 className="font-semibold text-sm">{column.title}</h3>
                  <Badge variant="secondary" className="text-xs">
                    {tasksByStatus[column.status]?.length || 0}
                  </Badge>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onTaskCreate?.(column.status)}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-3">
                <SortableContext items={tasksByStatus[column.status]?.map(t => t.id) || []}>
                  {tasksByStatus[column.status]?.map((task) => (
                    <TaskCard key={task.id} task={task} />
                  ))}
                </SortableContext>

                {(!tasksByStatus[column.status] || tasksByStatus[column.status].length === 0) && (
                  <div className="text-center py-8 text-gray-500">
                    <p className="text-sm">No tasks in this column</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        <DragOverlay>
          {activeTask ? (
            <TaskCard task={activeTask} isOverlay />
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  )
}