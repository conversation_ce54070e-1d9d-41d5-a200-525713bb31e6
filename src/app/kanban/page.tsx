"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { KanbanBoard } from '@/components/kanban-board'
import { TaskForm } from '@/components/task-form'
import { 
  LayoutDashboard, 
  FolderOpen, 
  CheckCircle, 
  Users, 
  Plus,
  ArrowLeft,
  Filter
} from 'lucide-react'
import Link from 'next/link'

interface Task {
  id: string
  title: string
  description?: string
  status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  assignee?: {
    id: string
    name: string
    avatar?: string
  }
  creator: {
    id: string
    name: string
    avatar?: string
  }
  dueDate?: string
  project: {
    id: string
    name: string
  }
  comments: number
  attachments: number
}

interface Project {
  id: string
  name: string
  description?: string
  status: string
  taskCount: number
}

export default function KanbanPage() {
  const [tasks, setTasks] = useState<Task[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [users, setUsers] = useState<Array<{ id: string; name: string }>>([])
  const [selectedProject, setSelectedProject] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [taskFormOpen, setTaskFormOpen] = useState(false)
  const [initialTaskStatus, setInitialTaskStatus] = useState<'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE' | 'CANCELLED'>('TODO')

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      // Fetch tasks
      const tasksResponse = await fetch('/api/tasks')
      if (tasksResponse.ok) {
        const tasksData = await tasksResponse.json()
        const formattedTasks = tasksData.map((task: any) => ({
          id: task.id,
          title: task.title,
          description: task.description,
          status: task.status,
          priority: task.priority,
          assignee: task.assignee ? {
            id: task.assignee.id,
            name: task.assignee.name,
            avatar: task.assignee.avatar
          } : undefined,
          creator: {
            id: task.creator.id,
            name: task.creator.name,
            avatar: task.creator.avatar
          },
          dueDate: task.dueDate,
          project: {
            id: task.project.id,
            name: task.project.name
          },
          comments: task._count.comments,
          attachments: 0 // Placeholder for now
        }))
        setTasks(formattedTasks)
      }

      // Fetch projects
      const projectsResponse = await fetch('/api/projects')
      if (projectsResponse.ok) {
        const projectsData = await projectsResponse.json()
        const formattedProjects = projectsData.map((project: any) => ({
          id: project.id,
          name: project.name,
          description: project.description,
          status: project.status,
          taskCount: project._count.tasks
        }))
        setProjects(formattedProjects)
      }

      // Fetch users (mock data for now)
      setUsers([
        { id: '1', name: 'John Doe' },
        { id: '2', name: 'Jane Smith' },
        { id: '3', name: 'Mike Johnson' },
        { id: '4', name: 'Sarah Wilson' },
      ])
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTaskUpdate = async (taskId: string, newStatus: Task['status']) => {
    try {
      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        setTasks(prevTasks =>
          prevTasks.map(task =>
            task.id === taskId ? { ...task, status: newStatus } : task
          )
        )
      }
    } catch (error) {
      console.error('Error updating task:', error)
    }
  }

  const handleTaskCreate = (status: Task['status']) => {
    setInitialTaskStatus(status)
    setTaskFormOpen(true)
  }

  const handleTaskSubmit = async (data: any) => {
    try {
      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          status: initialTaskStatus,
          dueDate: data.dueDate ? data.dueDate.toISOString() : null,
        }),
      })

      if (response.ok) {
        const newTask = await response.json()
        const formattedTask = {
          id: newTask.id,
          title: newTask.title,
          description: newTask.description,
          status: newTask.status,
          priority: newTask.priority,
          assignee: newTask.assignee ? {
            id: newTask.assignee.id,
            name: newTask.assignee.name,
            avatar: newTask.assignee.avatar
          } : undefined,
          creator: {
            id: newTask.creator.id,
            name: newTask.creator.name,
            avatar: newTask.creator.avatar
          },
          dueDate: newTask.dueDate,
          project: {
            id: newTask.project.id,
            name: newTask.project.name
          },
          comments: 0,
          attachments: 0
        }
        setTasks(prevTasks => [...prevTasks, formattedTask])
      }
    } catch (error) {
      console.error('Error creating task:', error)
    }
  }

  const filteredTasks = selectedProject === 'all' 
    ? tasks 
    : tasks.filter(task => task.project.id === selectedProject)

  const stats = {
    total: filteredTasks.length,
    todo: filteredTasks.filter(t => t.status === 'TODO').length,
    inProgress: filteredTasks.filter(t => t.status === 'IN_PROGRESS').length,
    review: filteredTasks.filter(t => t.status === 'REVIEW').length,
    done: filteredTasks.filter(t => t.status === 'DONE').length,
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading Kanban board...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="mr-4 flex items-center">
            <Link href="/">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Button>
            </Link>
            <span className="ml-4 font-bold">Kanban Board</span>
          </div>
          <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={selectedProject} onValueChange={setSelectedProject}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select project" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Projects</SelectItem>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              New Task
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container py-6">
        <div className="flex flex-col space-y-6">
          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-5">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">To Do</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-600">{stats.todo}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">In Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{stats.inProgress}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Review</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{stats.review}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Done</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.done}</div>
              </CardContent>
            </Card>
          </div>

          {/* Kanban Board */}
          <Card>
            <CardHeader>
              <CardTitle>Task Board</CardTitle>
              <CardDescription>
                Drag and drop tasks to update their status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <KanbanBoard
                tasks={filteredTasks}
                onTaskUpdate={handleTaskUpdate}
                onTaskCreate={handleTaskCreate}
              />
            </CardContent>
          </Card>

          <TaskForm
            open={taskFormOpen}
            onOpenChange={setTaskFormOpen}
            onSubmit={handleTaskSubmit}
            projects={projects}
            users={users}
          />
        </div>
      </main>
    </div>
  )
}