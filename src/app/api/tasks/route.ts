import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')

    const where = projectId ? { projectId } : {}

    const tasks = await db.task.findMany({
      where,
      include: {
        project: true,
        assignee: true,
        creator: true,
        comments: {
          include: {
            author: true
          }
        },
        _count: {
          select: {
            comments: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(tasks)
  } catch (error) {
    console.error('Error fetching tasks:', error)
    return NextResponse.json(
      { error: 'Failed to fetch tasks' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, description, projectId, priority, dueDate, assigneeId } = body

    // For demo purposes, we'll use a hardcoded creator ID
    // In a real app, this would come from the authenticated user
    const creatorId = 'demo-user-id'

    const task = await db.task.create({
      data: {
        title,
        description,
        projectId,
        priority,
        dueDate: dueDate ? new Date(dueDate) : null,
        assigneeId,
        creatorId,
        status: 'TODO'
      },
      include: {
        project: true,
        assignee: true,
        creator: true,
        comments: {
          include: {
            author: true
          }
        }
      }
    })

    return NextResponse.json(task, { status: 201 })
  } catch (error) {
    console.error('Error creating task:', error)
    return NextResponse.json(
      { error: 'Failed to create task' },
      { status: 500 }
    )
  }
}