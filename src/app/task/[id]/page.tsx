"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { TaskComments } from '@/components/task-comments'
import { 
  ArrowLeft,
  Edit,
  MessageSquare,
  Paperclip,
  Clock,
  User,
  Calendar,
  Flag
} from 'lucide-react'
import Link from 'next/link'

interface Comment {
  id: string
  content: string
  createdAt: string
  author: {
    id: string
    name: string
    avatar?: string
  }
}

interface Task {
  id: string
  title: string
  description?: string
  status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  assignee?: {
    id: string
    name: string
    avatar?: string
  }
  creator: {
    id: string
    name: string
    avatar?: string
  }
  dueDate?: string
  project: {
    id: string
    name: string
  }
  comments: Comment[]
  attachments: number
}

export default function TaskDetailPage() {
  const params = useParams()
  const router = useRouter()
  const taskId = params.id as string

  const [task, setTask] = useState<Task | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (taskId) {
      fetchTask()
    }
  }, [taskId])

  const fetchTask = async () => {
    try {
      // Fetch task details
      const taskResponse = await fetch(`/api/tasks?projectId=${taskId}`)
      if (taskResponse.ok) {
        const tasksData = await taskResponse.json()
        const foundTask = tasksData.find((t: any) => t.id === taskId)
        
        if (foundTask) {
          // Fetch comments for this task
          const commentsResponse = await fetch(`/api/comments?taskId=${taskId}`)
          const commentsData = commentsResponse.ok ? await commentsResponse.json() : []

          const formattedTask: Task = {
            id: foundTask.id,
            title: foundTask.title,
            description: foundTask.description,
            status: foundTask.status,
            priority: foundTask.priority,
            assignee: foundTask.assignee ? {
              id: foundTask.assignee.id,
              name: foundTask.assignee.name,
              avatar: foundTask.assignee.avatar
            } : undefined,
            creator: {
              id: foundTask.creator.id,
              name: foundTask.creator.name,
              avatar: foundTask.creator.avatar
            },
            dueDate: foundTask.dueDate,
            project: {
              id: foundTask.project.id,
              name: foundTask.project.name
            },
            comments: commentsData.map((comment: any) => ({
              id: comment.id,
              content: comment.content,
              createdAt: comment.createdAt,
              author: {
                id: comment.author.id,
                name: comment.author.name,
                avatar: comment.author.avatar
              }
            })),
            attachments: 0 // Placeholder for now
          }
          setTask(formattedTask)
        }
      }
    } catch (error) {
      console.error('Error fetching task:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddComment = async (content: string) => {
    try {
      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          taskId,
          authorId: 'demo-user-id' // In a real app, this would be the authenticated user
        }),
      })

      if (response.ok) {
        fetchTask() // Refresh task data
      }
    } catch (error) {
      console.error('Error adding comment:', error)
    }
  }

  const handleUpdateComment = async (commentId: string, content: string) => {
    try {
      const response = await fetch(`/api/comments/${commentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content }),
      })

      if (response.ok) {
        fetchTask() // Refresh task data
      }
    } catch (error) {
      console.error('Error updating comment:', error)
    }
  }

  const handleDeleteComment = async (commentId: string) => {
    try {
      const response = await fetch(`/api/comments/${commentId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        fetchTask() // Refresh task data
      }
    } catch (error) {
      console.error('Error deleting comment:', error)
    }
  }

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'TODO': return 'bg-gray-500'
      case 'IN_PROGRESS': return 'bg-blue-500'
      case 'REVIEW': return 'bg-yellow-500'
      case 'DONE': return 'bg-green-500'
      case 'CANCELLED': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'LOW': return 'bg-green-100 text-green-800'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800'
      case 'HIGH': return 'bg-orange-100 text-orange-800'
      case 'CRITICAL': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'No due date'
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading task details...</p>
        </div>
      </div>
    )
  }

  if (!task) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Task not found</h2>
          <Link href="/kanban">
            <Button>Back to Kanban Board</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="mr-4 flex items-center">
            <Link href="/kanban">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Kanban
              </Button>
            </Link>
          </div>
          <div className="flex flex-1 items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className={`w-3 h-3 rounded-full ${getStatusColor(task.status)}`} />
              <h1 className="text-xl font-semibold">{task.title}</h1>
              <Badge variant="outline" className={getPriorityColor(task.priority)}>
                {task.priority}
              </Badge>
            </div>
            <Button size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Edit Task
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Task Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                {task.description ? (
                  <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                    {task.description}
                  </p>
                ) : (
                  <p className="text-sm text-muted-foreground italic">
                    No description provided
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Comments */}
            <TaskComments
              taskId={task.id}
              comments={task.comments}
              onAddComment={handleAddComment}
              onUpdateComment={handleUpdateComment}
              onDeleteComment={handleDeleteComment}
            />
          </div>

          {/* Task Info Sidebar */}
          <div className="space-y-6">
            {/* Properties */}
            <Card>
              <CardHeader>
                <CardTitle>Properties</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Flag className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Status:</span>
                  <Badge variant="outline">{task.status.replace('_', ' ')}</Badge>
                </div>

                <div className="flex items-center space-x-2">
                  <Flag className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Priority:</span>
                  <Badge variant="outline" className={getPriorityColor(task.priority)}>
                    {task.priority}
                  </Badge>
                </div>

                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Due Date:</span>
                  <span className="text-sm text-muted-foreground">
                    {formatDate(task.dueDate)}
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Assignee:</span>
                  {task.assignee ? (
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={task.assignee.avatar} alt={task.assignee.name} />
                        <AvatarFallback className="text-xs">
                          {task.assignee.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{task.assignee.name}</span>
                    </div>
                  ) : (
                    <span className="text-sm text-muted-foreground">Unassigned</span>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Creator:</span>
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={task.creator.avatar} alt={task.creator.name} />
                      <AvatarFallback className="text-xs">
                        {task.creator.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm">{task.creator.name}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <MessageSquare className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Comments:</span>
                  <span className="text-sm text-muted-foreground">{task.comments.length}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <Paperclip className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Attachments:</span>
                  <span className="text-sm text-muted-foreground">{task.attachments}</span>
                </div>
              </CardContent>
            </Card>

            {/* Project */}
            <Card>
              <CardHeader>
                <CardTitle>Project</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">{task.project.name}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}