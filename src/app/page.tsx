"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  LayoutDashboard, 
  FolderOpen, 
  CheckCircle, 
  Clock, 
  Users, 
  Plus,
  MoreHorizontal,
  Search
} from 'lucide-react'
import { Input } from '@/components/ui/input'

// Mock data for development
const mockProjects = [
  {
    id: '1',
    name: 'Website Redesign',
    description: 'Complete overhaul of company website',
    status: 'ACTIVE',
    progress: 75,
    taskCount: 12,
    completedTasks: 9,
    members: 4,
    dueDate: '2024-03-15'
  },
  {
    id: '2',
    name: 'Mobile App Development',
    description: 'Native iOS and Android app',
    status: 'ACTIVE',
    progress: 45,
    taskCount: 20,
    completedTasks: 9,
    members: 6,
    dueDate: '2024-04-20'
  },
  {
    id: '3',
    name: 'Marketing Campaign',
    description: 'Q1 marketing initiatives',
    status: 'COMPLETED',
    progress: 100,
    taskCount: 8,
    completedTasks: 8,
    members: 3,
    dueDate: '2024-02-28'
  }
]

const mockTasks = [
  {
    id: '1',
    title: 'Design homepage mockup',
    project: 'Website Redesign',
    status: 'DONE',
    priority: 'HIGH',
    assignee: 'John Doe',
    dueDate: '2024-03-10'
  },
  {
    id: '2',
    title: 'Implement user authentication',
    project: 'Mobile App Development',
    status: 'IN_PROGRESS',
    priority: 'CRITICAL',
    assignee: 'Jane Smith',
    dueDate: '2024-03-12'
  },
  {
    id: '3',
    title: 'Create social media content',
    project: 'Marketing Campaign',
    status: 'TODO',
    priority: 'MEDIUM',
    assignee: 'Mike Johnson',
    dueDate: '2024-03-15'
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'ACTIVE': return 'bg-green-500'
    case 'COMPLETED': return 'bg-blue-500'
    case 'ON_HOLD': return 'bg-yellow-500'
    case 'ARCHIVED': return 'bg-gray-500'
    default: return 'bg-gray-500'
  }
}

const getTaskStatusColor = (status: string) => {
  switch (status) {
    case 'TODO': return 'bg-gray-500'
    case 'IN_PROGRESS': return 'bg-blue-500'
    case 'REVIEW': return 'bg-yellow-500'
    case 'DONE': return 'bg-green-500'
    case 'CANCELLED': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'LOW': return 'bg-green-100 text-green-800'
    case 'MEDIUM': return 'bg-yellow-100 text-yellow-800'
    case 'HIGH': return 'bg-orange-100 text-orange-800'
    case 'CRITICAL': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

export default function Dashboard() {
  const [searchTerm, setSearchTerm] = useState('')

  const stats = {
    totalProjects: mockProjects.length,
    activeProjects: mockProjects.filter(p => p.status === 'ACTIVE').length,
    totalTasks: mockProjects.reduce((acc, p) => acc + p.taskCount, 0),
    completedTasks: mockProjects.reduce((acc, p) => acc + p.completedTasks, 0)
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="mr-4 flex">
            <LayoutDashboard className="mr-2 h-6 w-6" />
            <span className="font-bold">ProjectHub</span>
          </div>
          <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
            <div className="w-full flex-1 md:w-auto md:flex-none">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search projects, tasks..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Button variant="ghost" size="sm">
                <FolderOpen className="mr-2 h-4 w-4" />
                Projects
              </Button>
              <Link href="/kanban">
                <Button variant="ghost" size="sm">
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Kanban Board
                </Button>
              </Link>
              <Button variant="ghost" size="sm">
                <Users className="mr-2 h-4 w-4" />
                Team
              </Button>
            </nav>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              New Project
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container py-6">
        <div className="flex flex-col space-y-6">
          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
                <FolderOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalProjects}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.activeProjects} active projects
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalTasks}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.completedTasks} completed
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">In Progress</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalTasks - stats.completedTasks}</div>
                <p className="text-xs text-muted-foreground">
                  Tasks in progress
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Team Members</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-xs text-muted-foreground">
                  Active team members
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Tabs */}
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="projects">Projects</TabsTrigger>
              <TabsTrigger value="tasks">Tasks</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                {/* Recent Projects */}
                <Card className="col-span-4">
                  <CardHeader>
                    <CardTitle>Recent Projects</CardTitle>
                    <CardDescription>Your latest project updates</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {mockProjects.map((project) => (
                      <div key={project.id} className="flex items-center space-x-4 rounded-lg border p-4">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(project.status)}`} />
                        <div className="flex-1 space-y-1">
                          <p className="text-sm font-medium leading-none">{project.name}</p>
                          <p className="text-xs text-muted-foreground">{project.description}</p>
                          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                            <span>{project.completedTasks}/{project.taskCount} tasks</span>
                            <span>•</span>
                            <span>{project.members} members</span>
                            <span>•</span>
                            <span>Due {project.dueDate}</span>
                          </div>
                          <Progress value={project.progress} className="h-2" />
                        </div>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </CardContent>
                </Card>

                {/* Recent Tasks */}
                <Card className="col-span-3">
                  <CardHeader>
                    <CardTitle>Recent Tasks</CardTitle>
                    <CardDescription>Latest task activities</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {mockTasks.map((task) => (
                      <div key={task.id} className="flex items-center space-x-3 rounded-lg border p-3">
                        <div className={`w-2 h-2 rounded-full ${getTaskStatusColor(task.status)}`} />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{task.title}</p>
                          <p className="text-xs text-muted-foreground">{task.project}</p>
                        </div>
                        <Badge variant="outline" className={getPriorityColor(task.priority)}>
                          {task.priority}
                        </Badge>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="projects" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>All Projects</CardTitle>
                  <CardDescription>Manage your projects and track progress</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockProjects.map((project) => (
                      <div key={project.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <div className={`w-3 h-3 rounded-full ${getStatusColor(project.status)}`} />
                            <h3 className="font-medium">{project.name}</h3>
                            <Badge variant="outline">{project.status}</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">{project.description}</p>
                          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                            <span>{project.completedTasks}/{project.taskCount} tasks</span>
                            <span>•</span>
                            <span>{project.members} members</span>
                            <span>•</span>
                            <span>Due {project.dueDate}</span>
                          </div>
                          <Progress value={project.progress} className="h-2 w-48" />
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="flex -space-x-2">
                            {[...Array(project.members)].map((_, i) => (
                              <Avatar key={i} className="h-6 w-6 border-2 background">
                                <AvatarFallback className="text-xs">JD</AvatarFallback>
                              </Avatar>
                            ))}
                          </div>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tasks" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>All Tasks</CardTitle>
                  <CardDescription>View and manage all tasks across projects</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockTasks.map((task) => (
                      <div key={task.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${getTaskStatusColor(task.status)}`} />
                            <h3 className="font-medium">{task.title}</h3>
                            <Badge variant="outline" className={getPriorityColor(task.priority)}>
                              {task.priority}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">{task.project}</p>
                          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                            <span>Assigned to {task.assignee}</span>
                            <span>•</span>
                            <span>Due {task.dueDate}</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback className="text-xs">JD</AvatarFallback>
                          </Avatar>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}