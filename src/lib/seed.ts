import { db } from '@/lib/db'

async function main() {
  // Create sample users
  const users = await Promise.all([
    db.user.create({
      data: {
        email: '<EMAIL>',
        name: '<PERSON>',
        role: 'MANAGER',
      },
    }),
    db.user.create({
      data: {
        email: '<EMAIL>',
        name: '<PERSON>',
        role: 'MEMBER',
      },
    }),
    db.user.create({
      data: {
        email: '<EMAIL>',
        name: '<PERSON>',
        role: 'MEMBER',
      },
    }),
    db.user.create({
      data: {
        email: '<EMAIL>',
        name: '<PERSON>',
        role: 'MEMBER',
      },
    }),
  ])

  // Create sample projects
  const projects = await Promise.all([
    db.project.create({
      data: {
        name: 'Website Redesign',
        description: 'Complete overhaul of company website with modern design',
        status: 'ACTIVE',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-03-31'),
        members: {
          create: [
            { userId: users[0].id, role: 'OWNER' },
            { userId: users[1].id, role: 'MEMBER' },
            { userId: users[2].id, role: 'MEMBER' },
          ],
        },
      },
    }),
    db.project.create({
      data: {
        name: 'Mobile App Development',
        description: 'Native iOS and Android app development',
        status: 'ACTIVE',
        startDate: new Date('2024-02-01'),
        endDate: new Date('2024-06-30'),
        members: {
          create: [
            { userId: users[0].id, role: 'OWNER' },
            { userId: users[1].id, role: 'MANAGER' },
            { userId: users[3].id, role: 'MEMBER' },
          ],
        },
      },
    }),
    db.project.create({
      data: {
        name: 'Marketing Campaign',
        description: 'Q1 marketing initiatives and campaigns',
        status: 'COMPLETED',
        startDate: new Date('2024-01-15'),
        endDate: new Date('2024-02-28'),
        members: {
          create: [
            { userId: users[1].id, role: 'OWNER' },
            { userId: users[2].id, role: 'MEMBER' },
          ],
        },
      },
    }),
  ])

  // Create sample tasks
  const tasks = await Promise.all([
    // Website Redesign tasks
    db.task.create({
      data: {
        title: 'Design homepage mockup',
        description: 'Create modern homepage design with new branding',
        status: 'DONE',
        priority: 'HIGH',
        projectId: projects[0].id,
        assigneeId: users[1].id,
        creatorId: users[0].id,
        dueDate: new Date('2024-03-10'),
      },
    }),
    db.task.create({
      data: {
        title: 'Implement responsive navigation',
        description: 'Create mobile-friendly navigation menu',
        status: 'IN_PROGRESS',
        priority: 'MEDIUM',
        projectId: projects[0].id,
        assigneeId: users[2].id,
        creatorId: users[0].id,
        dueDate: new Date('2024-03-15'),
      },
    }),
    db.task.create({
      data: {
        title: 'Update content pages',
        description: 'Rewrite and update all website content',
        status: 'TODO',
        priority: 'MEDIUM',
        projectId: projects[0].id,
        creatorId: users[0].id,
        dueDate: new Date('2024-03-20'),
      },
    }),

    // Mobile App Development tasks
    db.task.create({
      data: {
        title: 'Implement user authentication',
        description: 'Add secure login and registration functionality',
        status: 'IN_PROGRESS',
        priority: 'CRITICAL',
        projectId: projects[1].id,
        assigneeId: users[1].id,
        creatorId: users[0].id,
        dueDate: new Date('2024-03-12'),
      },
    }),
    db.task.create({
      data: {
        title: 'Design app interface',
        description: 'Create UI/UX designs for mobile app',
        status: 'DONE',
        priority: 'HIGH',
        projectId: projects[1].id,
        assigneeId: users[3].id,
        creatorId: users[0].id,
        dueDate: new Date('2024-02-28'),
      },
    }),
    db.task.create({
      data: {
        title: 'Setup database schema',
        description: 'Design and implement database structure',
        status: 'DONE',
        priority: 'HIGH',
        projectId: projects[1].id,
        assigneeId: users[0].id,
        creatorId: users[0].id,
        dueDate: new Date('2024-02-15'),
      },
    }),

    // Marketing Campaign tasks
    db.task.create({
      data: {
        title: 'Create social media content',
        description: 'Design and schedule social media posts',
        status: 'DONE',
        priority: 'MEDIUM',
        projectId: projects[2].id,
        assigneeId: users[2].id,
        creatorId: users[1].id,
        dueDate: new Date('2024-02-20'),
      },
    }),
    db.task.create({
      data: {
        title: 'Launch email campaign',
        description: 'Send out marketing emails to subscribers',
        status: 'DONE',
        priority: 'HIGH',
        projectId: projects[2].id,
        assigneeId: users[1].id,
        creatorId: users[1].id,
        dueDate: new Date('2024-02-25'),
      },
    }),
  ])

  // Create sample comments
  await Promise.all([
    db.comment.create({
      data: {
        content: 'Great work on the homepage design! The new layout looks much better.',
        taskId: tasks[0].id,
        authorId: users[0].id,
      },
    }),
    db.comment.create({
      data: {
        content: 'I need more time to complete the authentication feature. There are some security concerns I need to address.',
        taskId: tasks[3].id,
        authorId: users[1].id,
      },
    }),
    db.comment.create({
      data: {
        content: 'The social media content is ready for review. Please check the attached images.',
        taskId: tasks[6].id,
        authorId: users[2].id,
      },
    }),
  ])

  console.log('Database seeded successfully!')
  console.log(`Created ${users.length} users`)
  console.log(`Created ${projects.length} projects`)
  console.log(`Created ${tasks.length} tasks`)
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await db.$disconnect()
  })